[docks]

dock_3="Scene,Import"
dock_3_selected_tab_idx=0
dock_4="FileSystem"
dock_4_selected_tab_idx=0
dock_5="Inspector,Node,History"
dock_5_selected_tab_idx=0
dock_floating={}
dock_filesystem_h_split_offset=480
dock_filesystem_v_split_offset=0
dock_filesystem_display_mode=0
dock_filesystem_file_sort=0
dock_filesystem_file_list_display_mode=1
dock_filesystem_selected_paths=PackedStringArray("res://")
dock_filesystem_uncollapsed_paths=PackedStringArray("Favorites", "res://")
dock_node_current_tab=0
dock_history_include_scene=true
dock_history_include_global=true
dock_bottom=[]
dock_closed=[]
dock_split_2=0
dock_split_3=0
dock_hsplit_1=0
dock_hsplit_2=270
dock_hsplit_3=-270
dock_hsplit_4=0

[EditorNode]

open_scenes=PackedStringArray("res://scenes/SplashScreen.tscn")
current_scene="res://scenes/SplashScreen.tscn"
center_split_offset=0
selected_default_debugger_tab_idx=0
selected_main_editor_idx=1

[EditorWindow]

screen=0
mode="maximized"
position=Vector2i(0, 76)

[ScriptEditor]

open_scripts=[]
selected_script=""
open_help=[]
script_split_offset=400
list_split_offset=0
zoom_factor=1.0

[GameView]

floating_window_rect=Rect2i(0, 0, 0, 0)
floating_window_screen=-1

[ShaderEditor]

open_shaders=[]
split_offset=400
selected_shader=""
text_shader_zoom_factor=1.0
